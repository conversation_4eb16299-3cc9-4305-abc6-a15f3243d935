[2025-06-15T02:10:55.284Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:10:55.286Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:10:55.287Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:10:58.779Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:10:58.780Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:10:58.781Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 1,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:10:58.781Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:10:58.782Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:10:58.783Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 1,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:10:59.480Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:10:59.481Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:10:59.481Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:10:59.482Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:10:59.483Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:10:59.483Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:10:59.483Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:10:59.484Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:10:59.485Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:11:01.221Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true
}
[2025-06-15T02:11:01.221Z] ADDITIONS: Creating new additions line item
[2025-06-15T02:11:01.232Z] ADDITIONS: Created additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953461
}
[2025-06-15T02:11:01.233Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:01.234Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:01.235Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 1,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:01.236Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:01.237Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:01.238Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 1,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:01.576Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:01.576Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:01.587Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953461
}
[2025-06-15T02:11:01.587Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:01.588Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:01.589Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 1,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:01.589Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:01.590Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:01.591Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 1,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:01.744Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:01.745Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:01.747Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953461
}
[2025-06-15T02:11:01.747Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true
}
[2025-06-15T02:11:01.748Z] ADDITIONS: Creating new additions line item
[2025-06-15T02:11:01.749Z] ADDITIONS: Created additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953461
}
[2025-06-15T02:11:01.755Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:01.756Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:01.757Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:01.757Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:01.758Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:01.758Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:01.759Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:01.760Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:01.760Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:02.113Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:02.114Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:02.125Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953462
}
[2025-06-15T02:11:02.126Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:02.126Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:02.128Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953462
}
[2025-06-15T02:11:02.129Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:02.130Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:02.131Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:02.131Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:02.132Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:02.133Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:02.133Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:02.134Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:02.134Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:02.480Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:02.480Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:02.492Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953462
}
[2025-06-15T02:11:02.492Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:02.492Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:02.494Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953462
}
[2025-06-15T02:11:02.495Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:02.496Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:02.497Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:02.497Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:02.498Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:02.498Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:02.499Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:02.499Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:02.500Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:02.707Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:02.709Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:02.709Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:11:04.231Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:04.232Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:04.233Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 1,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:11:04.233Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:04.234Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:04.234Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 1,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:11:04.750Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:04.751Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:04.751Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:11:04.752Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:04.753Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:04.753Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:11:04.753Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:04.754Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:04.755Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:11:06.107Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true
}
[2025-06-15T02:11:06.108Z] ADDITIONS: Creating new additions line item
[2025-06-15T02:11:06.110Z] ADDITIONS: Created additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953466
}
[2025-06-15T02:11:06.114Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:06.115Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:06.116Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 1,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:06.116Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:06.117Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:06.117Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 1,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:06.461Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:06.461Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:06.471Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953466
}
[2025-06-15T02:11:06.472Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:06.473Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:06.473Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 1,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:06.474Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:06.474Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:06.475Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 1,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:07.445Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:07.446Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:07.449Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953467
}
[2025-06-15T02:11:07.449Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true
}
[2025-06-15T02:11:07.450Z] ADDITIONS: Creating new additions line item
[2025-06-15T02:11:07.451Z] ADDITIONS: Created additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953467
}
[2025-06-15T02:11:07.456Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:07.457Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:07.458Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:07.458Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:07.459Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:07.460Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:07.460Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:07.461Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:07.461Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:07.813Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:07.813Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:07.824Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953467
}
[2025-06-15T02:11:07.825Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:07.825Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:07.827Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953467
}
[2025-06-15T02:11:07.827Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:07.828Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:07.829Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:07.829Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:07.830Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:07.831Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:07.831Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:07.832Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:07.833Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:10.853Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:10.855Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:10.855Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:11.179Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:11.180Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:11.190Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953471
}
[2025-06-15T02:11:11.190Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:11.190Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:11.192Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953471
}
[2025-06-15T02:11:11.192Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:11.193Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:11.194Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:11.194Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:11.195Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:11.196Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:11.196Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:11.197Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:11.198Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:11.534Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:11.534Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:11.538Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953471
}
[2025-06-15T02:11:11.538Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:11.538Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:11.541Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953471
}
[2025-06-15T02:11:11.547Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:11.548Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:11.549Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:11.549Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:11.550Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:11.550Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:11.551Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:11.551Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:11.552Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:13.111Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:13.113Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:13.113Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:13.441Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:13.441Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:13.452Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953473
}
[2025-06-15T02:11:13.452Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:13.453Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:13.455Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953473
}
[2025-06-15T02:11:13.455Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:13.456Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:13.457Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:13.457Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:13.458Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:13.459Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:13.459Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:13.460Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:13.461Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:13.797Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:13.797Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:13.808Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953473
}
[2025-06-15T02:11:13.808Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:13.808Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:13.810Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953473
}
[2025-06-15T02:11:13.810Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:13.811Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:13.812Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:13.812Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:13.813Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:13.814Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:13.814Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:13.815Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:13.816Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:15.056Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:15.057Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:15.058Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:15.396Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:15.396Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:15.400Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953475
}
[2025-06-15T02:11:15.400Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:15.400Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:15.402Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953475
}
[2025-06-15T02:11:15.407Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:15.409Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:15.409Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:15.410Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:15.411Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:15.412Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:15.412Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:15.413Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:15.413Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:15.747Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:15.748Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:15.760Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953475
}
[2025-06-15T02:11:15.760Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:15.761Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:15.762Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953475
}
[2025-06-15T02:11:15.762Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:15.764Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:15.764Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:15.765Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:15.766Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:15.766Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:15.767Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:15.767Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:15.768Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:17.629Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:17.630Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:17.631Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:17.962Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:17.963Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:17.974Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953477
}
[2025-06-15T02:11:17.974Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:17.974Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:17.977Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953477
}
[2025-06-15T02:11:17.977Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:17.978Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:17.979Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:17.979Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:17.980Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:17.981Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:17.981Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:17.982Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:17.983Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:18.329Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:18.330Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:18.333Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953478
}
[2025-06-15T02:11:18.333Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:18.333Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:18.335Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953478
}
[2025-06-15T02:11:18.340Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:18.342Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:18.342Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:18.343Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:18.343Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:18.344Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:18.344Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:18.345Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:18.346Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:18.684Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:18.685Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:18.695Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953478
}
[2025-06-15T02:11:18.695Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:18.696Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:18.698Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953478
}
[2025-06-15T02:11:18.698Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:18.699Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:18.700Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:18.700Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:18.701Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:18.702Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:18.702Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:18.703Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:18.703Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:19.713Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:19.714Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:19.715Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:20.046Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:20.047Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:20.057Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953480
}
[2025-06-15T02:11:20.057Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:20.057Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:20.059Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953480
}
[2025-06-15T02:11:20.060Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:20.061Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:20.061Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:20.062Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:20.063Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:20.063Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:20.064Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:20.064Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:20.065Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:20.400Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:20.400Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:20.411Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953480
}
[2025-06-15T02:11:20.411Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:20.412Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:20.416Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953480
}
[2025-06-15T02:11:20.417Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:20.418Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:20.418Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:20.419Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:20.419Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:20.420Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:20.420Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:20.421Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:20.422Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:30.069Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "132b41b5-4923-4abd-b4b4-fd93f81ecd24"
}
[2025-06-15T02:11:30.070Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "132b41b5-4923-4abd-b4b4-fd93f81ecd24",
  "payslipId": "444c74dd-2587-4c83-b1b8-250c40b8d67d",
  "payslipExists": true
}
[2025-06-15T02:11:30.071Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "132b41b5-4923-4abd-b4b4-fd93f81ecd24",
  "payslipId": "444c74dd-2587-4c83-b1b8-250c40b8d67d",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:11:31.175Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:31.176Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:31.176Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:31.512Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:31.513Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:31.525Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953491
}
[2025-06-15T02:11:31.525Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:31.526Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:31.528Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953491
}
[2025-06-15T02:11:31.528Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:31.529Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:31.530Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:31.530Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:31.531Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:31.532Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:31.532Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:31.533Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:31.533Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:31.879Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:31.880Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:31.891Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953491
}
[2025-06-15T02:11:31.892Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:31.892Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:31.894Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953491
}
[2025-06-15T02:11:31.895Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:31.897Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:31.897Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:31.898Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:31.899Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:31.899Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:31.900Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:31.900Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:31.901Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:32.003Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:32.004Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:32.005Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:32.345Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:32.345Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:32.368Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953492
}
[2025-06-15T02:11:32.368Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:32.368Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:32.371Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953492
}
[2025-06-15T02:11:32.371Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:32.372Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:32.372Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:32.373Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:32.373Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:32.374Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:32.374Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:32.375Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:32.376Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:32.714Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:32.715Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:11:32.727Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953492
}
[2025-06-15T02:11:32.727Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:32.727Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:11:32.730Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953492
}
[2025-06-15T02:11:32.730Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:32.731Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:32.731Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:32.732Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:32.732Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:32.733Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:32.733Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:32.734Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:11:32.735Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:34.756Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:34.757Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:34.758Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:35.096Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:35.096Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:35.108Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953495
}
[2025-06-15T02:11:35.109Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:35.109Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:35.111Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953495
}
[2025-06-15T02:11:35.111Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:35.112Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:35.113Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:35.114Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:35.115Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:35.115Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:35.116Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:35.117Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:35.117Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:35.463Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:35.464Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:11:35.474Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953495
}
[2025-06-15T02:11:35.475Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:35.475Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:11:35.477Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953495
}
[2025-06-15T02:11:35.477Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:35.478Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:35.478Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:35.479Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:35.480Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:35.480Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:11:35.481Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:11:35.482Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:11:35.483Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:03.636Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:03.637Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:03.638Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:04.079Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:13:04.080Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:13:04.090Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953584
}
[2025-06-15T02:13:04.091Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:13:04.091Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:13:04.093Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953584
}
[2025-06-15T02:13:04.093Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:04.094Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:04.095Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:04.096Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:04.097Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:04.098Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:04.098Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:04.099Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:04.100Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:04.442Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:13:04.442Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:13:04.452Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953584
}
[2025-06-15T02:13:04.453Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:13:04.453Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:13:04.455Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953584
}
[2025-06-15T02:13:04.456Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:04.457Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:04.458Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:04.458Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:04.459Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:04.460Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:04.460Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:04.461Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:04.462Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:06.179Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:06.180Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:06.181Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:06.537Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:06.537Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:06.548Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953586
}
[2025-06-15T02:13:06.548Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:06.548Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:06.550Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953586
}
[2025-06-15T02:13:06.550Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:06.551Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:06.552Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:06.552Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:06.553Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:06.554Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:06.554Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:06.555Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:06.556Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:06.897Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:06.897Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:06.908Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953586
}
[2025-06-15T02:13:06.908Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:06.908Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:06.910Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953586
}
[2025-06-15T02:13:06.911Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:06.912Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:06.912Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:06.913Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:06.914Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:06.915Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:06.915Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:06.916Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:06.916Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:10.550Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:10.552Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:10.552Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:10.553Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:10.553Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:10.554Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:10.905Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:10.905Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:10.915Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953590
}
[2025-06-15T02:13:10.915Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:10.915Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:10.917Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953590
}
[2025-06-15T02:13:10.922Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:10.923Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:10.924Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:10.925Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:10.926Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:10.927Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:10.927Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:10.928Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:10.928Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:10.929Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:10.930Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:10.930Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:10.930Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:10.931Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:10.932Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:11.288Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:11.289Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:11.300Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953591
}
[2025-06-15T02:13:11.301Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:11.301Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:11.303Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953591
}
[2025-06-15T02:13:11.307Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:11.308Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:11.308Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:11.309Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:11.310Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:11.311Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:11.311Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:11.312Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:11.312Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:11.313Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:11.313Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:11.314Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:11.314Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:11.315Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:11.316Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:11.672Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:11.672Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:11.683Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953591
}
[2025-06-15T02:13:11.683Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:11.684Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:11.686Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953591
}
[2025-06-15T02:13:11.689Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:11.690Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:11.691Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:11.691Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:11.692Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:11.692Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:11.693Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:11.694Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:11.695Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:11.699Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:11.700Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:11.701Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:11.701Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:11.702Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:11.703Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:12.875Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:12.876Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:12.877Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:13.207Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:13:13.207Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:13:13.218Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953593
}
[2025-06-15T02:13:13.218Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:13:13.218Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:13:13.220Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953593
}
[2025-06-15T02:13:13.221Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:13.222Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:13.222Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:13.222Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:13.223Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:13.224Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:13.224Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:13.225Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:13.226Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:13.574Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:13:13.574Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:13:13.577Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953593
}
[2025-06-15T02:13:13.577Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:13:13.577Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:13:13.579Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953593
}
[2025-06-15T02:13:13.584Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:13.585Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:13.586Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:13.586Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:13.587Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:13.588Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:13.588Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:13.589Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:13:13.589Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:13:15.666Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:15.668Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:15.668Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:15.996Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:15.996Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:16.007Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953595
}
[2025-06-15T02:13:16.007Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:16.007Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:16.010Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953596
}
[2025-06-15T02:13:16.016Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.017Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.017Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.018Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.019Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.020Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.020Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.021Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.021Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.022Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.022Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.023Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.023Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.024Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.025Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.374Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:16.375Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:16.386Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953596
}
[2025-06-15T02:13:16.386Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:16.386Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:16.388Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953596
}
[2025-06-15T02:13:16.393Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.394Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.395Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.395Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.396Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.397Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.397Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.398Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.399Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.399Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.400Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.401Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.401Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.402Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.402Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.748Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:16.748Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:13:16.759Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953596
}
[2025-06-15T02:13:16.759Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:16.759Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:13:16.762Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953596
}
[2025-06-15T02:13:16.766Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.767Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.768Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.768Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.769Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.770Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.770Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.771Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.772Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.772Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.773Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.774Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:13:16.774Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:13:16.775Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:13:16.776Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:30.730Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:30.731Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:30.732Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:31.161Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:31.161Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:31.171Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953671
}
[2025-06-15T02:14:31.172Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:31.172Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:31.174Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953671
}
[2025-06-15T02:14:31.174Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:31.175Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:31.176Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:31.176Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:31.178Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:31.179Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:31.179Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:31.181Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:31.182Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:31.526Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:31.527Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:31.538Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953671
}
[2025-06-15T02:14:31.539Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:31.539Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:31.542Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953671
}
[2025-06-15T02:14:31.542Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:31.543Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:31.544Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:31.544Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:31.545Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:31.546Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:31.546Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:31.547Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:31.548Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:32.575Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:32.577Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:32.577Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:32.926Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:32.927Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:32.931Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953672
}
[2025-06-15T02:14:32.931Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:32.931Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:32.933Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953672
}
[2025-06-15T02:14:32.945Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:32.946Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:32.947Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:32.947Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:32.948Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:32.949Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:32.950Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:32.951Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:32.952Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:32.952Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:32.953Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:32.954Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:32.954Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:32.955Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:32.956Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:33.304Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:33.304Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:33.316Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953673
}
[2025-06-15T02:14:33.316Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:33.317Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:33.318Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953673
}
[2025-06-15T02:14:33.324Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:33.325Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:33.326Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:33.326Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:33.327Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:33.328Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:33.328Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:33.329Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:33.330Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:33.330Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:33.331Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:33.332Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:33.332Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:33.333Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:33.333Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:33.691Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:33.692Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:33.702Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953673
}
[2025-06-15T02:14:33.703Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:33.703Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:33.705Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953673
}
[2025-06-15T02:14:33.709Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:33.710Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:33.711Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:33.711Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:33.712Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:33.713Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:33.713Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:33.714Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:33.714Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:33.714Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:33.715Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:33.716Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:33.716Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:33.717Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:33.717Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:33.960Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:33.961Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:33.962Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:34.289Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:34.289Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:34.300Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953674
}
[2025-06-15T02:14:34.300Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:34.300Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:34.302Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953674
}
[2025-06-15T02:14:34.303Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:34.304Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:34.304Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:34.305Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:34.305Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:34.306Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:34.306Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:34.307Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:34.308Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:34.645Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:34.645Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:34.656Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953674
}
[2025-06-15T02:14:34.656Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:34.657Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:34.659Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953674
}
[2025-06-15T02:14:34.659Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:34.660Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:34.661Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:34.661Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:34.662Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:34.663Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:34.663Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:34.664Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:34.665Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:34.694Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:34.695Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:34.696Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:35.027Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:35.028Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:35.039Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953675
}
[2025-06-15T02:14:35.039Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:35.039Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:35.042Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953675
}
[2025-06-15T02:14:35.045Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:35.046Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:35.047Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:35.047Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:35.048Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:35.048Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:35.049Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:35.050Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:35.050Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:35.054Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:35.055Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:35.055Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:35.056Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:35.057Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:35.058Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:35.408Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:35.409Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:35.411Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953675
}
[2025-06-15T02:14:35.411Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:35.412Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:35.414Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953675
}
[2025-06-15T02:14:35.425Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:35.426Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:35.427Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:35.427Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:35.428Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:35.429Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:35.429Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:35.430Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:35.431Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:35.431Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:35.432Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:35.432Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:35.433Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:35.433Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:35.434Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:35.878Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "132b41b5-4923-4abd-b4b4-fd93f81ecd24"
}
[2025-06-15T02:14:35.879Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "132b41b5-4923-4abd-b4b4-fd93f81ecd24",
  "payslipId": "1da988bb-f9f6-4fa6-8cb2-bab3e7797273",
  "payslipExists": true
}
[2025-06-15T02:14:35.879Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "132b41b5-4923-4abd-b4b4-fd93f81ecd24",
  "payslipId": "1da988bb-f9f6-4fa6-8cb2-bab3e7797273",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:14:36.379Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "93913cf9-892e-4195-b03a-d09eef9cc483"
}
[2025-06-15T02:14:36.380Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "93913cf9-892e-4195-b03a-d09eef9cc483",
  "payslipId": "9a834197-b760-43f0-b4e6-a0ed717b4fe6",
  "payslipExists": true
}
[2025-06-15T02:14:36.381Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "93913cf9-892e-4195-b03a-d09eef9cc483",
  "payslipId": "9a834197-b760-43f0-b4e6-a0ed717b4fe6",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:14:36.787Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "6cf48df6-61e2-47f3-acbf-a2f9b00e4532"
}
[2025-06-15T02:14:36.788Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "6cf48df6-61e2-47f3-acbf-a2f9b00e4532",
  "payslipId": "de593c39-8e91-4013-9831-05ca60300380",
  "payslipExists": true
}
[2025-06-15T02:14:36.789Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "6cf48df6-61e2-47f3-acbf-a2f9b00e4532",
  "payslipId": "de593c39-8e91-4013-9831-05ca60300380",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:14:37.552Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:37.554Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:37.554Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:37.893Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:37.893Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:37.897Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953677
}
[2025-06-15T02:14:37.897Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:37.897Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:37.899Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953677
}
[2025-06-15T02:14:37.913Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:37.914Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:37.915Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:37.915Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:37.916Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:37.917Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:37.917Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:37.918Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:37.919Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:37.919Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:37.920Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:37.920Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:37.920Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:37.921Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:37.922Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:38.276Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:38.276Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:38.288Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953678
}
[2025-06-15T02:14:38.288Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:38.288Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:38.290Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953678
}
[2025-06-15T02:14:38.297Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:38.298Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:38.299Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:38.299Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:38.300Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:38.301Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:38.301Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:38.302Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:38.303Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:38.303Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:38.304Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:38.305Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:38.306Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:38.306Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:38.307Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:38.666Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:38.668Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:38.668Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:38.993Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:38.993Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:39.004Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953678
}
[2025-06-15T02:14:39.004Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:39.005Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:39.007Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953679
}
[2025-06-15T02:14:39.008Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:39.009Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:39.010Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:39.010Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:39.011Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:39.012Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:39.012Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:39.013Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:39.013Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:39.350Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:39.350Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:39.360Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953679
}
[2025-06-15T02:14:39.361Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:39.361Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:39.363Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953679
}
[2025-06-15T02:14:39.364Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:39.365Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:39.365Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:39.366Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:39.367Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:39.367Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:39.367Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:39.368Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:39.369Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:39.703Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:39.703Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:39.713Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953679
}
[2025-06-15T02:14:39.714Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:39.714Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:39.716Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953679
}
[2025-06-15T02:14:39.716Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:39.717Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:39.718Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:39.718Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:39.719Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:39.720Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:39.720Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:39.721Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:39.721Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:40.937Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:40.938Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:40.939Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:40.939Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:40.940Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:40.940Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:40.941Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:40.941Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:40.942Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:41.293Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:41.293Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:41.303Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953681
}
[2025-06-15T02:14:41.304Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:41.304Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:41.306Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953681
}
[2025-06-15T02:14:41.307Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:41.308Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:41.309Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:41.309Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:41.310Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:41.310Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:41.310Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:41.311Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:41.312Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:41.651Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:41.651Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:41.662Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953681
}
[2025-06-15T02:14:41.663Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:41.663Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:41.665Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953681
}
[2025-06-15T02:14:41.667Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:41.668Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:41.669Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:41.669Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:41.670Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:41.671Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:41.671Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:41.672Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:41.673Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:42.573Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:42.574Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:42.575Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:42.575Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:42.576Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:42.576Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:42.577Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:42.577Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:42.578Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:42.703Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:42.704Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:42.705Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:42.705Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:42.706Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:42.706Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:42.706Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:42.707Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:42.708Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:42.916Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:42.917Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:42.919Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953682
}
[2025-06-15T02:14:42.919Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:42.919Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:42.921Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953682
}
[2025-06-15T02:14:42.925Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:42.926Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:42.927Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:42.927Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:42.929Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:42.929Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:42.930Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:42.930Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:42.931Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:43.276Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:43.277Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:43.289Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953683
}
[2025-06-15T02:14:43.289Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:43.289Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:43.292Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953683
}
[2025-06-15T02:14:43.292Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:43.293Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:43.294Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:43.294Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:43.295Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:43.296Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:43.296Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:43.297Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:43.298Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:43.638Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:43.639Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:43.640Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:43.640Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:43.641Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:43.641Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:43.641Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:43.642Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:43.643Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:43.643Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:43.643Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:43.645Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953683
}
[2025-06-15T02:14:43.645Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:43.645Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:43.648Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953683
}
[2025-06-15T02:14:43.660Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:43.661Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:43.662Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:43.662Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:43.663Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:43.663Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:43.664Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:43.664Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:43.665Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:43.974Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:43.976Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:43.976Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:43.977Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:43.978Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:43.978Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:43.978Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:43.979Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:43.980Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:44.323Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:44.323Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:44.335Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953684
}
[2025-06-15T02:14:44.335Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:44.335Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:44.338Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953684
}
[2025-06-15T02:14:44.338Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:44.339Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:44.340Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:44.340Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:44.341Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:44.341Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:44.341Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:44.342Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:44.343Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:44.693Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:44.694Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:44.703Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953684
}
[2025-06-15T02:14:44.704Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:44.704Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:44.707Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953684
}
[2025-06-15T02:14:44.707Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:44.708Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:44.709Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:44.709Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:44.710Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:44.710Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:44.711Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:44.711Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:44.712Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:47.551Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:47.551Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:47.562Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953687
}
[2025-06-15T02:14:47.562Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:47.562Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:47.564Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953687
}
[2025-06-15T02:14:47.564Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:47.565Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:47.566Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:47.566Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:47.567Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:47.567Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:47.568Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:47.568Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:47.569Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:47.919Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:47.920Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:47.923Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953687
}
[2025-06-15T02:14:47.924Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:47.924Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:47.926Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953687
}
[2025-06-15T02:14:47.931Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:47.932Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:47.933Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:47.933Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:47.934Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:47.935Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:47.935Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:47.936Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:47.936Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 0
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:49.161Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:49.161Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:49.172Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953689
}
[2025-06-15T02:14:49.172Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:49.173Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:49.175Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953689
}
[2025-06-15T02:14:49.176Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:49.177Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:49.177Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:49.178Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:49.179Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:49.179Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:49.180Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:49.181Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:49.181Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:49.526Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:49.526Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:49.536Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953689
}
[2025-06-15T02:14:49.537Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:49.537Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:49.538Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953689
}
[2025-06-15T02:14:49.540Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:49.541Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:49.541Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:49.541Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:49.542Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:49.543Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:49.543Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:49.544Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:49.545Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 0
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:51.060Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:51.060Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:51.071Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953691
}
[2025-06-15T02:14:51.071Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:51.071Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:51.074Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953691
}
[2025-06-15T02:14:51.075Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:51.076Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:51.077Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:51.077Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:51.078Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:51.078Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:51.078Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:51.079Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:51.080Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:51.421Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:51.422Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:51.433Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953691
}
[2025-06-15T02:14:51.433Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:51.433Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:51.436Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953691
}
[2025-06-15T02:14:51.436Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:51.437Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:51.438Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:51.438Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:51.439Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:51.439Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:51.439Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:51.440Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:51.441Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:53.382Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:53.383Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:53.384Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:53.725Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:53.726Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:53.736Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953693
}
[2025-06-15T02:14:53.737Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:53.737Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:53.739Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953693
}
[2025-06-15T02:14:53.745Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:53.746Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:53.747Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:53.748Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:53.749Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:53.749Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:53.750Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:53.750Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:53.751Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:53.751Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:53.752Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:53.753Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:53.753Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:53.754Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:53.754Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:54.106Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:54.107Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:54.118Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953694
}
[2025-06-15T02:14:54.118Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:54.118Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:54.120Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953694
}
[2025-06-15T02:14:54.128Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:54.129Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:54.130Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:54.131Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:54.132Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:54.133Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:54.133Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:54.134Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:54.135Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:54.135Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:54.136Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:54.136Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:54.136Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:54.137Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:54.138Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:54.492Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:54.493Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:54.496Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953694
}
[2025-06-15T02:14:54.496Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:54.496Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:54.498Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953694
}
[2025-06-15T02:14:54.511Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:54.513Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:54.513Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:54.513Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:54.514Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:54.515Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:54.515Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:54.516Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:54.516Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:54.517Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:54.517Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:54.518Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:54.518Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:54.519Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:54.520Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:54.891Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:54.892Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:54.893Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:55.218Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:55.219Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:55.231Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953695
}
[2025-06-15T02:14:55.231Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:55.231Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:55.233Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953695
}
[2025-06-15T02:14:55.234Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:55.235Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:55.235Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:55.237Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:55.238Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:55.238Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:55.238Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:55.239Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:55.240Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:55.580Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:55.581Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:55.591Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953695
}
[2025-06-15T02:14:55.592Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:55.592Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:55.594Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953695
}
[2025-06-15T02:14:55.595Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:55.596Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:55.596Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:55.597Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:55.597Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:55.598Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:55.598Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:55.599Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:55.600Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:55.700Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:55.702Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:55.702Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:56.033Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:56.034Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:56.045Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953696
}
[2025-06-15T02:14:56.045Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:56.045Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:56.047Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953696
}
[2025-06-15T02:14:56.051Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:56.052Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:56.053Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:56.053Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:56.054Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:56.054Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:56.055Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:56.055Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:56.056Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:56.056Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:56.057Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:56.058Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:56.058Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:56.059Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:56.059Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:56.195Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:56.196Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:56.196Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:56.524Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:56.525Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:56.536Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953696
}
[2025-06-15T02:14:56.537Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:56.537Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:56.539Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953696
}
[2025-06-15T02:14:56.539Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:56.540Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:56.541Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:56.541Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:56.542Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:56.542Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:56.542Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:56.543Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:56.544Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:56.761Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:56.762Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:56.763Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:57.092Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:57.099Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:14:57.101Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953697
}
[2025-06-15T02:14:57.101Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:57.101Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:14:57.103Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953697
}
[2025-06-15T02:14:57.108Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:57.109Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:57.110Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:57.110Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:57.111Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:57.112Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:57.112Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:57.113Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:57.113Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:57.113Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:57.114Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:57.115Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:57.115Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:57.116Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:14:57.117Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:14:57.261Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:57.262Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:57.263Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:57.593Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:57.593Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:57.604Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953697
}
[2025-06-15T02:14:57.604Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:57.604Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:57.606Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953697
}
[2025-06-15T02:14:57.612Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:57.614Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:57.614Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:57.614Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:57.615Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:57.616Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:57.616Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:57.617Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:57.618Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:57.959Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:57.959Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:57.962Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953697
}
[2025-06-15T02:14:57.963Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:57.963Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:57.965Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953697
}
[2025-06-15T02:14:57.972Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:57.973Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:57.974Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:57.974Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:57.975Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:57.976Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:57.976Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:57.977Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:57.977Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:58.865Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:58.866Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:58.867Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:58.867Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:58.868Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:58.868Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:58.869Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:58.869Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:58.870Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:59.209Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:59.209Z] ADDITIONS: Updating existing additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39"
}
[2025-06-15T02:14:59.221Z] ADDITIONS: Updated additions line item {
  "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "bonus",
  "amount": 555,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953699
}
[2025-06-15T02:14:59.223Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:59.223Z] ADDITIONS: Updating existing additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61"
}
[2025-06-15T02:14:59.226Z] ADDITIONS: Updated additions line item {
  "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
  "payslip_id": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "item_type_id": "commission",
  "amount": 667,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953461,
  "updated_at": 1749953699
}
[2025-06-15T02:14:59.226Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:59.227Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:59.227Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:59.228Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:59.228Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:59.229Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:59.229Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:59.230Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:59.231Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 0,
  "additionsData": [
    {
      "id": "ac9819df-d7d3-4f6e-acf6-3a06ddcf5f39",
      "item_type_id": "bonus",
      "amount": 555
    },
    {
      "id": "c07be86b-9280-4f97-9ad9-60835ae7ff61",
      "item_type_id": "commission",
      "amount": 667
    }
  ],
  "deductionsData": []
}
[2025-06-15T02:14:59.574Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:14:59.575Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:14:59.576Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:15:00.311Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:00.312Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:15:00.312Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:15:01.321Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:01.322Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:01.322Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:01.660Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:15:01.660Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:15:01.671Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953701
}
[2025-06-15T02:15:01.671Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:15:01.671Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:15:01.673Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953701
}
[2025-06-15T02:15:01.680Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:01.681Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:01.682Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:01.682Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:01.683Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:01.683Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:01.683Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:01.685Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:01.685Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:01.685Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:01.686Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:01.687Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:01.687Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:01.688Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:01.689Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:02.043Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:15:02.043Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:15:02.056Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953702
}
[2025-06-15T02:15:02.057Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:15:02.057Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:15:02.059Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953702
}
[2025-06-15T02:15:02.068Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:02.069Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:02.070Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:02.070Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:02.071Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:02.072Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:02.072Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:02.073Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:02.073Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:02.074Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:02.075Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:02.075Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:02.075Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:02.076Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:02.077Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:02.219Z] getPayslip called {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:02.220Z] Found payslip {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "payslipExists": true
}
[2025-06-15T02:15:02.221Z] Fetched payslip data {
  "employeeId": "b26f574c-0c0f-4958-a72e-5fad25addcba",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "fb5c1f7c-055d-4360-a43b-37bf0c5a8523",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
[2025-06-15T02:15:02.842Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:02.843Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:02.844Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:03.176Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:15:03.176Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:15:03.188Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953703
}
[2025-06-15T02:15:03.188Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:15:03.189Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:15:03.191Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953703
}
[2025-06-15T02:15:03.199Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:03.200Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:03.200Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:03.201Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:03.202Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:03.202Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:03.203Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:03.203Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:03.204Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:03.204Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:03.205Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:03.205Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:03.206Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:03.207Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:03.207Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:03.559Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:15:03.560Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:15:03.572Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953703
}
[2025-06-15T02:15:03.572Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:15:03.572Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:15:03.574Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953703
}
[2025-06-15T02:15:03.583Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:03.584Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:03.585Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:03.585Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:03.586Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:03.586Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:03.586Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:03.587Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:03.588Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:03.588Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:03.589Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:03.590Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:03.590Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:03.591Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:03.591Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 2,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.126Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.128Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.128Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.128Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.129Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.129Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.130Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.130Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.131Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.471Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:15:17.471Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:15:17.482Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953717
}
[2025-06-15T02:15:17.482Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:15:17.483Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:15:17.484Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953717
}
[2025-06-15T02:15:17.489Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.490Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.491Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.491Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.492Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.492Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.493Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.494Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.495Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.495Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.496Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.496Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.496Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.497Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.498Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.846Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:15:17.846Z] ADDITIONS: Updating existing additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf"
}
[2025-06-15T02:15:17.857Z] ADDITIONS: Updated additions line item {
  "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "holiday",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953466,
  "updated_at": 1749953717
}
[2025-06-15T02:15:17.858Z] ADDITIONS: Upserting additions line item {
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:15:17.858Z] ADDITIONS: Updating existing additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c"
}
[2025-06-15T02:15:17.867Z] ADDITIONS: Updated additions line item {
  "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
  "payslip_id": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "item_type_id": "expenses",
  "amount": 0,
  "is_repeating": true,
  "zeroise_next": true,
  "created_at": 1749953467,
  "updated_at": 1749953717
}
[2025-06-15T02:15:17.870Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.871Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.872Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.872Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.873Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.874Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.874Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.875Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.875Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.882Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.883Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.883Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:17.884Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:17.885Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:17.885Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 2,
  "deductionsCount": 1,
  "additionsData": [
    {
      "id": "e2d35f6b-f404-44f3-b0ff-2eff68c695cf",
      "item_type_id": "holiday",
      "amount": 0
    },
    {
      "id": "2b83c5fb-53a9-449b-a124-5c23b3742b4c",
      "item_type_id": "expenses",
      "amount": 0
    }
  ],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:18.120Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:18.121Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:18.122Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 1,
  "additionsData": [],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:18.463Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:18.464Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:18.465Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 1,
  "additionsData": [],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:18.465Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:18.466Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:18.466Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 1,
  "additionsData": [],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:18.806Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:18.807Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:18.808Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 1,
  "additionsData": [],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:18.808Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:18.809Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:18.810Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 1,
  "additionsData": [],
  "deductionsData": [
    {
      "id": "a38e7c93-55c4-49b1-87c9-adeef5366dec",
      "item_type_id": "salary-sacrifice",
      "amount": 0
    }
  ]
}
[2025-06-15T02:15:18.966Z] getPayslip called {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513"
}
[2025-06-15T02:15:18.967Z] Found payslip {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "payslipExists": true
}
[2025-06-15T02:15:18.967Z] Fetched payslip data {
  "employeeId": "a353bbf7-6424-45f2-889a-811059ab982d",
  "periodId": "daf5eff0-1e31-4375-86a9-b38d1c547513",
  "payslipId": "5d96ae36-23e9-422c-90b0-90360aafc239",
  "itemsCount": 0,
  "additionsCount": 0,
  "deductionsCount": 0,
  "additionsData": [],
  "deductionsData": []
}
