# Deductions Section Implementation Plan

## Overview

Implement the deductions section UI-to-database connection, applying all lessons learned from the successful additions section implementation.

## Key Lessons Learned from Additions Section

### ✅ Critical Issues to Avoid

1. **Missing Data Field in Service**: Ensure `deductions` field is returned from `getPayslip` service function
2. **State Closure Issues**: Use refs to prevent stale state in setTimeout callbacks
3. **Duplicate Type Restrictions**: Don't prevent multiple items of the same type
4. **One-Step-Behind Saving**: Implement immediate saving with proper state management
5. **UI Persistence**: Ensure data loads correctly when navigating between periods

### ✅ Successful Patterns to Replicate

1. **Immediate Saving**: Both manual and auto-save using refs for current state
2. **Real Database IDs**: Proper handling of temporary vs real database IDs
3. **Enhanced Data Loading**: Proper useEffect dependencies and data mapping
4. **Unique ID Generation**: Use timestamp + random for truly unique temporary IDs

## Implementation Steps

### Phase 1: Backend Database Schema (Already Exists)

- ✅ `payslip_deductions_item_types` table exists
- ✅ `payslip_deductions_line_items` table exists
- ✅ Backend IPC handlers exist in `src/electron/ipc/payslips.ts`

### Phase 2: Service Layer Updates

**File: `src/services/employerDbService.ts`**

1. **Add Deductions Type Import**

   ```typescript
   import type {
     PayslipDeductionsLineItem,
   } from "../drizzle/schema/employer/payslip";
   ```

2. **Update getPayslip Return Type**

   ```typescript
   Promise<{
     payslip: Payslip | null;
     items: PayslipLineItem[];
     notes: PayslipNote[];
     additions: PayslipAdditionsLineItem[];
     deductions: PayslipDeductionsLineItem[]; // ADD THIS
   }>
   ```

3. **Update getPayslip Return Statement**
   ```typescript
   return {
     payslip: result.payslip,
     items: result.items,
     notes: result.notes,
     additions: result.additions,
     deductions: result.deductions, // ADD THIS
   };
   ```

### Phase 3: React Query Hooks

**File: `src/hooks/tanstack-query/usePayslipDeductions.ts`**

Create hooks similar to additions:

- `usePayslipDeductionsItemTypes()`
- `useUpsertPayslipDeductionsLineItemMutation()`
- `useDeletePayslipDeductionsLineItemMutation()`
- `useClearAllPayslipDeductionsLineItemsMutation()`
- `useDeleteAllPayslipDeductionsLineItemsMutation()`

### Phase 4: Component Implementation

**File: `src/components/payroll/payslip-sections/deductions-section.tsx`**

#### Core Structure (Copy from additions-section.tsx)

```typescript
interface DeductionItem {
  id: string;
  type: string;
  name: string;
  amount: number;
  isRepeating: boolean;
  zeroizeNext: boolean;
}

interface DeductionsSectionData {
  items: DeductionItem[];
}
```

#### Critical Implementation Details

1. **State Management with Refs**

   ```typescript
   const itemsRef = useRef<DeductionItem[]>([]);

   // Keep ref in sync with state
   useEffect(() => {
     itemsRef.current = items;
   }, [items]);
   ```

2. **Proper Data Loading**

   ```typescript
   useEffect(() => {
     if (data && itemTypes) {
       const deductions = (data as any).deductions || [];
       const items = deductions
         .map((item: any) => {
           const typeMeta = itemTypes.find((t) => t.id === item.item_type_id);
           if (!typeMeta) return null;
           return {
             id: item.id, // Use real database ID
             type: typeMeta.code,
             name: typeMeta.display_label,
             amount: item.amount,
             isRepeating: item.is_repeating,
             zeroizeNext: item.zeroise_next,
           } as DeductionItem;
         })
         .filter(Boolean) as DeductionItem[];

       // Force update with spread operator
       setDeductionsData({ items: [...items] });
       initRef.current = true;
     }
   }, [data, itemTypes, employeeId, periodId]);
   ```

3. **Immediate Saving with Refs**

   ```typescript
   const saveChanges = () => {
     const currentItems = itemsRef.current;
     // Use currentItems instead of stale state
   };
   ```

4. **No Duplicate Type Restrictions**

   ```typescript
   const addDeductionItem = (type: string, name: string) => {
     // NO duplicate checking - allow multiple of same type
     const newItem: DeductionItem = {
       id: `temp-${type}-${Date.now()}-${Math.random()}`,
       type,
       name,
       amount: 0,
       isRepeating: true,
       zeroizeNext: true,
     };
     setDeductionsData({ items: [...items, newItem] });
     setTimeout(() => saveChanges(), 100);
   };
   ```

5. **Proper ID Handling**
   ```typescript
   const isRealDbId = item.id && !item.id.startsWith("temp-");
   if (isRealDbId) {
     payload.id = item.id;
   }
   ```

### Phase 5: Deduction Types to Implement

Based on typical payroll deductions:

- Income Tax
- National Insurance
- Pension Contributions
- Student Loan
- Court Orders
- Union Dues
- Other Deductions

### Phase 6: Testing Checklist

#### ✅ Core Functionality

- [ ] Add deduction items (multiple types)
- [ ] Add multiple items of same type
- [ ] Set amounts and save immediately
- [ ] Toggle repeating/zeroize checkboxes
- [ ] Delete individual items
- [ ] Clear all values
- [ ] Delete all items

#### ✅ Data Persistence

- [ ] Items save to database immediately
- [ ] No "one step behind" behavior
- [ ] Last item saves properly
- [ ] Navigate to different period and back - items persist
- [ ] App restart - items load correctly

#### ✅ Edge Cases

- [ ] Empty periods show correctly
- [ ] Large amounts save properly
- [ ] Boolean values save correctly
- [ ] Real vs temporary ID handling

## Common Pitfalls to Watch For

1. **Missing deductions field in service return** - This was the main UI persistence issue
2. **State closure in setTimeout** - Use refs for current state
3. **Duplicate prevention logic** - Don't restrict multiple items of same type
4. **useEffect dependencies** - Include employeeId and periodId
5. **Console logging in Electron** - Renderer logs don't show reliably

## Success Criteria

When complete, the deductions section should:

- ✅ Save immediately without lag
- ✅ Allow multiple items of same type
- ✅ Persist data across navigation
- ✅ Handle all data types correctly
- ✅ Provide excellent user experience

## Files to Modify

1. `src/services/employerDbService.ts` - Add deductions field
2. `src/hooks/tanstack-query/usePayslipDeductions.ts` - Create hooks
3. `src/components/payroll/payslip-sections/deductions-section.tsx` - Main component
4. Import deductions component in parent payslip component

## Estimated Time

- Phase 2-3: 30 minutes (service + hooks)
- Phase 4: 2-3 hours (component implementation)
- Phase 5-6: 1 hour (testing and refinement)

**Total: 3.5-4.5 hours**

The additions section took longer due to discovery of issues. With this plan, deductions should be much faster to implement.
